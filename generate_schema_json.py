#!/usr/bin/env python3
"""
This script reads a text file containing filenames (one per line) and generates
JSON objects with those filenames as the contentUrl values.

Usage:
    python generate_schema_json.py filenames.txt > schema.json
"""

import sys
import json

def generate_schema_json(filenames):
    """
    Generate a JSON array of ImageObject entries with the given filenames as contentUrl values.
    
    Args:
        filenames (list): List of filenames to use as contentUrl values
        
    Returns:
        str: Formatted JSON string
    """
    # Create the JSON objects
    json_objects = []
    
    for filename in filenames:
        # Skip empty lines
        if not filename.strip():
            continue
            
        # Create the JSON object
        json_obj = {
            "@context": "https://schema.org",
            "@type": "ImageObject",
            "contentUrl": filename.strip(),
            "creditText": "Rockwood Resort Motel"
        }
        
        json_objects.append(json_obj)
    
    # Format the JSON with indentation
    formatted_json = json.dumps(json_objects, indent=4)
    
    # Wrap in script tag
    return f'<script type="application/ld+json">\n{formatted_json}\n</script>'

def main():
    # Check if a filename was provided
    if len(sys.argv) < 2:
        print("Usage: python generate_schema_json.py filenames.txt", file=sys.stderr)
        sys.exit(1)
    
    # Read the filenames from the file
    filename = sys.argv[1]
    try:
        with open(filename, 'r') as f:
            filenames = f.readlines()
    except FileNotFoundError:
        print(f"Error: File '{filename}' not found", file=sys.stderr)
        sys.exit(1)
    
    # Generate the JSON
    json_output = generate_schema_json(filenames)
    
    # Print the JSON to stdout
    print(json_output)

if __name__ == "__main__":
    main()
