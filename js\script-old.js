// Toggle Menu Function
function toggleMenu() {
    const menuOverlay = document.getElementById('menuOverlay');
    menuOverlay.classList.toggle('active');
}

// Close menu when clicking on a menu link
document.addEventListener('DOMContentLoaded', function () {
    const menuLinks = document.querySelectorAll('.menu-links a');

    menuLinks.forEach(link => {
        link.addEventListener('click', function () {
            const menuOverlay = document.getElementById('menuOverlay');
            menuOverlay.classList.remove('active');
        });
    });

    // Add scroll event listener for header
    window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });

    // Initialize animations on scroll
    initAnimations();
});

// Initialize animations for elements
function initAnimations() {
    // Get all elements with animate__animated class
    const animatedElements = document.querySelectorAll('.animate__animated');

    // Create an Intersection Observer
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            // If element is in viewport
            if (entry.isIntersecting) {
                // Get the animation class (e.g., animate__fadeIn)
                const animationClass = Array.from(entry.target.classList).find(
                    className => className.startsWith('animate__') && className !== 'animate__animated'
                );

                // Remove the animation class
                if (animationClass) {
                    entry.target.classList.remove(animationClass);
                }

                // Add the animation class after a small delay to trigger it again
                setTimeout(() => {
                    if (animationClass) {
                        entry.target.classList.add(animationClass);
                    }
                }, 50);

                // Unobserve the element after animating it
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1 // Trigger when at least 10% of the element is visible
    });

    // Observe each animated element
    animatedElements.forEach(element => {
        // Remove the animation classes initially to prevent them from animating on page load
        const animationClass = Array.from(element.classList).find(
            className => className.startsWith('animate__') && className !== 'animate__animated'
        );

        if (animationClass) {
            element.classList.remove(animationClass);
        }

        // Start observing the element
        observer.observe(element);
    });
}

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();

        const targetId = this.getAttribute('href');

        if (targetId === '#') return;

        const targetElement = document.querySelector(targetId);

        if (targetElement) {
            window.scrollTo({
                top: targetElement.offsetTop - 70, // Offset for fixed header
                behavior: 'smooth'
            });
        }
    });
});
