// Toggle Menu Function
function toggleMenu() {
    const menuOverlay = document.getElementById('menuOverlay');
    menuOverlay.classList.toggle('active');
}

// Close menu when clicking on a menu link
document.addEventListener('DOMContentLoaded', function () {
    const menuLinks = document.querySelectorAll('.menu-links a');

    menuLinks.forEach(link => {
        link.addEventListener('click', function () {
            const menuOverlay = document.getElementById('menuOverlay');
            menuOverlay.classList.remove('active');
        });
    });

    const today = new Date().toISOString().split('T')[0]; // Format: YYYY-MM-DD
    const calendarURL = `https://events.timely.fun/wirnq7ob/posterboard?start_date=${today}&lang=en-US`;

    const calendarDiv = document.getElementById('timely-calendar');
    calendarDiv.innerHTML = `
        <iframe src="${calendarURL}" width="100%" height="800" style="border:none;"></iframe>
    `;

    // Add scroll event listener for header
    window.addEventListener('scroll', function () {
        const rockwoodLogo = document.querySelector('.rockwood');

        if (window.scrollY >= 800 && window.scrollY <= 1800) {
            // Hide logo while in this scroll range
            rockwoodLogo.style.opacity = '0';
            rockwoodLogo.style.pointerEvents = 'none';
        } else {
            // Show logo outside this range
            rockwoodLogo.style.opacity = '1';
            rockwoodLogo.style.pointerEvents = 'auto';
        }
    });

});

document.addEventListener('click', function (event) {
    const menuOverlay = document.getElementById('menuOverlay');
    const menuHamburger = document.getElementById('menuHamburger');

    if (menuOverlay.classList.contains('active') && !menuHamburger.contains(event.target)) {
        console.log("Clicked outside menu");
        if (!menuOverlay.contains(event.target)) { // could combine but for testing
            menuOverlay.classList.remove('active');
        }
    }
});