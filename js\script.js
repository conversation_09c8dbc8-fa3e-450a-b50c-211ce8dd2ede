// Toggle Menu Function
function toggleMenu() {
    const menuOverlay = document.getElementById('menuOverlay');
    menuOverlay.classList.toggle('active');
}

// Close menu when clicking on a menu link
document.addEventListener('DOMContentLoaded', function () {
    const menuLinks = document.querySelectorAll('.menu-links a');

    menuLinks.forEach(link => {
        link.addEventListener('click', function () {
            const menuOverlay = document.getElementById('menuOverlay');
            menuOverlay.classList.remove('active');
        });
    });

    // Add scroll event listener for header
    // window.addEventListener('scroll', function () {
    //     const header = document.querySelector('header');
    //     if (window.scrollY > 50) {
    //         header.classList.add('scrolled');
    //     } else {
    //         header.classList.remove('scrolled');
    //     }
    // });
});

document.addEventListener('click', function (event) {
    const menuOverlay = document.getElementById('menuOverlay');
    const menuHamburger = document.getElementById('menuHamburger');

    if (menuOverlay.classList.contains('active') && !menuHamburger.contains(event.target)) {
        console.log("Clicked outside menu");
        if (!menuOverlay.contains(event.target)) { // could combine but for testing
            menuOverlay.classList.remove('active');
        }
    }
});