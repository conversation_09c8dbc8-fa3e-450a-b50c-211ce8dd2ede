import os
import requests
from io import BytesIO
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from PIL import Image, ImageStat

# --- CONFIG ---
INPUT_HTML  = 'restaurants.html'
OUTPUT_HTML = 'output.html'
OUT_DIR     = 'images/sights'
os.makedirs(OUT_DIR, exist_ok=True)


def detect_border_color(img, sample_px=10, thresh=128):
    gray = img.convert('L')
    w, h = gray.size

    # grab 10×10px from each corner
    corners = [
        gray.crop((0, 0, sample_px, sample_px)),             # top-left
        gray.crop((w-sample_px, 0, w, sample_px)),           # top-right
        gray.crop((0, h-sample_px, sample_px, h)),           # bottom-left
        gray.crop((w-sample_px, h-sample_px, w, h)),         # bottom-right
    ]

    # weighted mean over all corner pixels
    total = 0
    count = 0
    for corner in corners:
        stat = ImageStat.Stat(corner)
        pixels = corner.size[0] * corner.size[1]
        total += stat.mean[0] * pixels
        count += pixels

    mean_brightness = total / count
    print(f'Border mean brightness: {mean_brightness:.1f}')   # debug

    # bright corners → white bg; dark corners → black bg
    return (255,255,255) if mean_brightness > thresh else (0,0,0)

with open(INPUT_HTML, 'r', encoding='utf-8') as f:
    soup = BeautifulSoup(f, 'html.parser')

for card in soup.select('.sight-card'):
    print (card.name)
    img_tag = card.find('img')
    h5_tag  = card.find('h5')
    if not (img_tag and h5_tag):
        continue

    src = img_tag['src']
    if not urlparse(src).netloc:
        base = 'https://funlake.com'
        src  = urljoin(base, src)

    label    = h5_tag.get_text(strip=True)
    ext       = os.path.splitext(src)[1] or '.jpg'
    filename  = label.replace(' ', '_') + ext
    out_path  = os.path.join(OUT_DIR, filename)

    resp = requests.get(src, timeout=10)
    resp.raise_for_status()
    img  = Image.open(BytesIO(resp.content)).convert('RGB')

    #scale image a bit to make it look better.
    scale = 2.5   # 5% bigger; tweak as you like
    w0, h0 = img.size
    w1, h1 = int(w0 * scale), int(h0 * scale)
    img  = img.resize((w1, h1), Image.LANCZOS)

    # pick padding color by sampling the image border
    bg_col = detect_border_color(img)

    w, h    = img.size
    size    = max(w, h)
    canvas  = Image.new('RGB', (size, size), bg_col)
    offset  = ((size - w) // 2, (size - h) // 2)
    canvas.paste(img, offset)

    if size > 500:
        final = canvas.resize((500, 500), Image.LANCZOS)
    else:
        final = canvas

    final.save(out_path)

    img_tag['src'] = f'{OUT_DIR}/{filename}'

    print (f'Processed {filename}')

with open(OUTPUT_HTML, 'w', encoding='utf-8') as f:
    f.write(str(soup))

print('Done — images padded to square with matching border color and HTML updated.')
