@font-face {
    font-family: 'MurrayHill Bd BT Bold';
    font-style: normal;
    font-weight: normal;
    src: url('font/MURYHILB.woff') format('woff');
}

@font-face {
    font-family: 'FreeSerif';
    src: url('font/FreeSerifBold.woff2') format('woff2'),
        url('font/FreeSerifBold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
}

:root {
    --primary-color: #333;
    --secondary-color: #f8f9fa;
    --accent-color: rgb(182, 0, 0);
    --tertiary-color: #007bff;
    --text-color: #333;
    --light-text: #f8f9fa;
    --dark-bg: #222;
    --light-bg: #f8f9fa;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Roboto', sans-serif;
    font-size: 12px;
    color: var(--text-color);
    overflow-x: hidden;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: 'Roboto', serif;
    font-weight: bold;
}

section {
    padding: 80px 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

/* ===== NAVIGATION ===== */
header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    padding: 20px 0;
    transition: var(--transition);
    background-color: transparent;
}

header.scrolled {
    padding: 10px 0;
}

.logo {
    font-family: 'FreeSerif', serif;
    font-size: 24px;
    color: var(--light-text);
    text-decoration: none;
    font-weight: bold;
    transition: var(--transition);
}

.rockwood {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1100;
    font-family: 'MurrayHill Bd BT Bold', Sans-serif;
    font-size: clamp(1.5rem, 5vh, 3.25rem);
    background: radial-gradient(circle at center, #ff0000, rgb(82, 1, 1));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    -webkit-text-stroke: 1.7px black;
    box-shadow: 0 0 1px rgb(138, 41, 41), 0 0 10px rgb(75, 30, 30), 0 0 15px rgba(110, 0, 0, 0.7), inset 0 0 8px rgba(153, 13, 13, 0.8);
    padding: clamp(5px, 1vh, 10px) clamp(7px, 2vh, 20px);
    border-radius: 15px;
    border: 1px solid var(--accent-color);
    text-align: center;
}

.resortmotel {
    border-top: 2px solid var(--accent-color);
    font-family: 'FreeSerif', serif;
    font-size: clamp(1rem, 2vh, 2rem);
    font-weight: bold;
    text-transform: uppercase;
}

.phone {
    text-decoration: none;
    color: #ffd000;
}

.phone-bar {
    position: fixed;
    top: 50%;
    transform: translateY(-50%);
    padding: 20px 8px 17px 10px;
    margin-left: 2px;
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.522);
    border-radius: 25px;
}

.phone-bar span {
    display: block;
    margin: 5px 5px;
}

.phone-bar i {
    color: rgb(65, 65, 255);
    -webkit-text-stroke: 1px black;
}

.phone-bar a {
    font-family: 'Franklin Gothic Medium', sans-serif;
    font-size: clamp(1rem, 2vh, 3rem);
    color: #ffd622;
    text-decoration: none;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 2px;
    line-height: 1;
}

.phone-bar a:hover {
    text-shadow:
        -1px -1px 4px #000,
        1px -1px 4px #000,
        -1px 1px 4px #000,
        1px 1px 4px #000;
}

.rw {
    font-family: 'MurrayHill Bd BT Bold', serif;
    font-size: 30px;
    background: radial-gradient(circle at center, #ff0000, rgb(82, 1, 1));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    -webkit-text-stroke: 1.7px black;
    text-transform: uppercase;
}

.rw:hover {
    text-shadow: 0 0 1px rgb(138, 41, 41), 0 0 10px rgb(75, 30, 30), 0 0 15px rgba(110, 0, 0, 0.7), inset 0 0 8px rgba(153, 13, 13, 0.8);
}

.rm {
    font-family: 'FreeSerif', serif;
    font-size: 20px;
    background: radial-gradient(circle at center, #ff0000, rgb(82, 1, 1));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    -webkit-text-stroke: 1.7px black;
    text-transform: uppercase;
    padding-left: 14.5px;
}

header.scrolled .logo {
    color: var(--primary-color);
}

.video {
    padding: 50px 50px;
}

.menu-btn {
    background: transparent;
    border: none;
    color: var(--light-text);
    font-size: 24px;
    cursor: pointer;
    transition: var(--transition);
    z-index: 1001;
}

header.scrolled .menu-btn {
    color: var(--primary-color);
}

.menu-overlay {
    position: fixed;
    top: 0;
    left: -100%;
    width: 30vh;
    height: 100vh;
    background-color: var(--dark-bg);
    z-index: 1001;
    transition: var(--transition);
    overflow-y: auto;
}

.menu-overlay.active {
    left: 0;
}

.menu-content {
    padding: 50px 30px;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    background: transparent;
    border: none;
    color: var(--light-text);
    font-size: 24px;
    cursor: pointer;
}

.menu-links {
    display: flex;
    flex-direction: column;
    margin-top: 50px;
}

.menu-links a {
    color: var(--light-text);
    text-decoration: none;
    font-size: clamp(1.2rem, 2vh, 1.5rem);
    margin-bottom: 20px;
    transition: var(--transition);
    font-weight: 300;
}

.menu-links a:hover {
    color: var(--accent-color);
    transform: translateX(10px);
}

/* ===== HERO SECTION ===== */
.hero-section {
    height: 100vh;
    background: url('../images/hero.webp');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--light-text);
    padding: 0;
    margin: 0;
    background-color: var(--light-bg);
}

.hero-content {
    max-width: 700px;
    padding: 0 20px;
}

.hero-content h1 {
    font-size: 4rem;
    margin-bottom: 20px;
    font-weight: bold;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    line-height: 1.6;
}

.cta-button {

    display: inline-block;
    padding: 12px 30px;
    background-color: var(--accent-color);
    color: var(--light-text);
    text-decoration: none;
    border-radius: 30px;
    font-weight: 600;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button:hover {
    background-color: var(--accent-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    color: var(--light-text);
}


/* ===== EVENTS SECTION ===== */
#timely-calendar iframe {
    border-radius: 10px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5);
    border: 1px solid #ccc;
    width: 100%;
    height: 70vh;
}

/* ===== OUR STORY SECTION ===== */
.our-story-section {
    background-color: var(--light-bg);
}

.our-story-section h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
}

.our-story-section h2:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--accent-color);
}

.our-story-section img {
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* ===== AMENITIES SECTION ===== */
.amenities-section {
    background-color: #fff;
}

.amenities-section h2 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.amenities-section h6 {
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 20px;
}

.amenity-card {
    padding: 30px;
    border-radius: 10px;
    background-color: var(--light-bg);
    height: 100%;
    transition: var(--transition);
}

.amenity-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.amenity-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.amenity-card p {
    font-size: 1rem;
    line-height: 1.6;
}

/* ===== GALLERY SECTION ===== */
.gallery-section {
    background-color: var(--light-bg);
    padding-bottom: 100px;
}

.gallery-section h2 {
    font-size: 2.5rem;
    margin-bottom: 40px;
}

.carousel {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.carousel-item img {
    height: 800px;
    object-fit: cover;
}

.gallery-grid {
    margin-top: 30px;
}

.gallery-item {
    margin-bottom: 30px;
}

.gallery-item img {
    border-radius: 10px;
    transition: var(--transition);
    height: 250px;
    width: 100%;
    object-fit: cover;
}

.gallery-item img:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.gallery-tagline {
    font-size: 2rem;
    margin-top: 50px;
    color: var(--accent-color);
}

/* ===== REVIEWS SECTION ===== */
.reviews-section {
    background-color: #fff;
    padding: 100px 0;
}

.reviews-section h2 {
    font-size: 2.5rem;
    margin-bottom: 50px;
}

.review-card {
    background-color: var(--light-bg);
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    position: relative;
    transition: var(--transition);
}

.review-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.review-card p {
    font-size: 1.1rem;
    line-height: 1.8;
    font-style: italic;
    margin-bottom: 20px;
}

.review-card h4 {
    font-size: 1.2rem;
    color: var(--accent-color);
}

/* ===== SIGHTS SECTION ===== */
.events-section {
    background-color: var(--light-bg);
    padding: 100px 0;
}

.events-section h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.events-section>.container>.row>.col-12>p {
    font-size: 1.1rem;
    margin-bottom: 50px;
}

.events-card {
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    transition: var(--transition);
}

.events-card a {
    text-decoration: none;
}

.events-card:hover {
    transform: translateX(10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.events-card h3 {
    font-size: 1.3rem;
    color: var(--accent-color);
    margin-bottom: 10px;
}

.events-card p {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 0;
}

/* Sights Section Styles */
.sights-section {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.sights-section h2 {
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.sights-section>.container>.row>.col-12>p {
    font-size: 1.1rem;
    margin-bottom: 50px;
}

/* Tab Navigation Styles */
.nav-tabs {
    border-bottom: 3px solid var(--accent-color);
    margin-bottom: 2rem;
}

.nav-tabs .nav-link {
    border: none;
    background: transparent;
    color: var(--text-color);
    font-weight: 600;
    font-size: 1.1rem;
    padding: 15px 30px;
    margin-right: 10px;
    border-radius: 10px 10px 0 0;
    transition: var(--transition);
}

.nav-tabs .nav-link:hover {
    background-color: rgba(192, 57, 43, 0.1);
    color: var(--accent-color);
    border-color: transparent;
}

.nav-tabs .nav-link.active {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.nav-tabs .nav-link i {
    margin-right: 8px;
}

/* Sight Card Styles */
.sight-card {
    background-color: #fff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.sight-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.sight-image {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.sight-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: var(--transition);
}

.sight-card:hover .sight-image img {
    transform: scale(1.05);
}

.sight-content {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.sight-content h5 {
    color: var(--primary-color);
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.sight-content p {
    color: var(--text-color);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 15px;
    flex-grow: 1;
}

.sight-content .btn {
    margin-top: auto;
    align-self: flex-start;
}

/* Empty State Styles */
.empty-state {
    text-align: center;
    padding: 60px 20px;
}

.empty-state i {
    color: #dee2e6;
    margin-bottom: 20px;
}

.empty-state h4 {
    color: #6c757d;
    margin-bottom: 10px;
}

.empty-state p {
    color: #6c757d;
    font-size: 1rem;
}

/* ===== FOOTER ===== */
footer {
    background-color: var(--dark-bg);
    color: var(--light-text);
    padding: 25px 50px 20px 50px;
}

footer h3 {
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: var(--accent-color);
}

.contact-info {
    font-size: clamp(0.75rem, 3vw, 1.5rem);
    line-height: 1.8;
}

.contact-info i {
    margin-right: 10px;
    color: var(--accent-color);
}

.contact-info a {
    color: var(--light-text);
    text-decoration: none;
    transition: var(--transition);
}

.contact-info a:hover {
    color: var(--accent-color);
}

.social-icons {
    margin-top: 20px;
}

.social-icon {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: var(--accent-color);
    color: var(--dark-bg);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    margin: 0 10px;
    transition: var(--transition);
}

.social-icon:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.newsletter-form .form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    color: var(--light-text);
    padding: 12px 15px;
}

.newsletter-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-form .btn-primary {
    background-color: var(--accent-color);
    border: none;
    padding: 10px 25px;
    font-weight: 600;
    transition: var(--transition);
}

.newsletter-form .btn-primary:hover {
    background-color: #c09c2c;
    transform: translateY(-3px);
}

.copyright {
    margin-top: 50px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 991px) {
    section {
        padding: 60px 0;
    }

    .hero-content h1 {
        font-size: 3rem;
    }

    .carousel-item img {
        height: 400px;
    }
}

@media (max-width: 767px) {
    section {
        padding: 50px 0;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .our-story-section img {
        margin-bottom: 30px;
    }

    .carousel-item img {
        height: 300px;
    }

    .gallery-item img {
        height: 200px;
    }

    footer {
        text-align: center;
    }

    .contact-info,
    .newsletter-form {
        margin-bottom: 30px;
    }
}

@media (max-width: 575px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .carousel-item img {
        height: 250px;
    }

    .gallery-item img {
        height: 150px;
    }
}

/* Animation delays for staggered animations */
.animate__fadeInUp:nth-child(2) {
    animation-delay: 0.2s;
}

.animate__fadeInUp:nth-child(3) {
    animation-delay: 0.4s;
}